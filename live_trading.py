#!/usr/bin/env python3
# live_trading.py
"""
Live‑trading slučka s Agentom SAC a TradeExecutor‑om,
ktorý používa identickú risk‑/TP/SL logiku ako backtest.
"""
from __future__ import annotations
import os, sys, time, json, logging, argparse, threading, asyncio
from pathlib import Path
from datetime import datetime, timedelta, timezone
from queue import Queue, Empty
from typing import Dict, Any, List, Optional, Tuple
import hashlib
import random

import numpy as np
import pandas as pd
import torch, requests, websockets
from websockets.exceptions import ConnectionClosedError

# Import our new modules
from config_loader import ConfigLoader
from fallback_data_provider import FallbackDataProvider

try:
    from binance.client import Client as BinanceClient
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    BinanceClient = None
    print("WARNING: python-binance not installed. Live trading disabled.")
    print("Install with: pip install python-binance")
from stable_baselines3 import SAC
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

from scalping_env import ScalpingEnv
from indicators import calculate_and_merge_indicators
from trade_executor import TradeExecutor                # <‑‑ náš nový modul
from data_provider import DataProvider
from popart_sac import PopArtSAC                        # <-- PopArt SAC support
from agent import SimpleCNN1D, SafeReplayBuffer         # <-- Pre PopArt loading

# ──────────────────────────── LOGGING ─────────────────────────────────────
logging.basicConfig(
    level   = logging.INFO,
    format  = "%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt = "%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout),
              logging.FileHandler("live_trading.log")]
)
log = logging.getLogger("LiveTrading")

def safe_read_parquet(file_path, **kwargs):
    """
    Safe wrapper around pd.read_parquet that validates data quality.
    Detects and handles corrupted market data with 0.00000 prices.
    """
    try:
        df = pd.read_parquet(file_path, **kwargs)
        
        # Check if this is market OHLC data
        ohlc_cols = ['open', 'high', 'low', 'close']
        available_ohlc = [col for col in ohlc_cols if col in df.columns]
        
        if available_ohlc:
            # Check for corrupted 0.00000 prices in OHLC data
            for col in available_ohlc:
                zero_mask = (df[col] == 0.0) | (df[col].isna())
                if zero_mask.any():
                    corrupted_count = zero_mask.sum()
                    total_rows = len(df)
                    
                    if corrupted_count > 0:
                        log.warning(f"⚠️ DATA CORRUPTION DETECTED in {file_path}")
                        log.warning(f"   Column '{col}' has {corrupted_count}/{total_rows} corrupted (0.00000) values")
                        
                        # Show first few corrupted timestamps for debugging
                        if 'timestamp' in df.columns:
                            corrupted_rows = df[zero_mask]
                            for idx, row in corrupted_rows.head(3).iterrows():
                                timestamp = pd.to_datetime(row['timestamp'], utc=True) if pd.notna(row['timestamp']) else 'Unknown'
                                ohlc_vals = {c: row.get(c, 'N/A') for c in available_ohlc}
                                log.warning(f"   {timestamp}: {ohlc_vals}")
                        
                        # CRITICAL: Remove corrupted rows to prevent trading losses
                        # Only remove rows where ANY OHLC value is 0.0 or NaN
                        corruption_mask = pd.Series(False, index=df.index)
                        for ohlc_col in available_ohlc:
                            corruption_mask |= (df[ohlc_col] == 0.0) | (df[ohlc_col].isna())
                        
                        if corruption_mask.any():
                            clean_df = df[~corruption_mask].copy()
                            removed_count = len(df) - len(clean_df)
                            log.warning(f"   🧹 CLEANED: Removed {removed_count} corrupted rows")
                            log.warning(f"   ✅ SAFE DATA: {len(clean_df)} clean rows remaining")
                            df = clean_df
                        
                        # If too much data is corrupted, raise error
                        if len(df) < total_rows * 0.5:  # More than 50% corrupted
                            raise ValueError(f"File {file_path} has too much corrupted data ({corrupted_count}/{total_rows} rows). Cannot proceed safely.")
        
        return df
        
    except Exception as e:
        log.error(f"Error reading {file_path}: {e}")
        raise

DEFAULT_CONFIG_PATH = "strategyConfig_scalp_1s.json"
COINAPI_WS_URL      = "wss://ws.coinapi.io/v1/"
COINAPI_REST_URL    = "https://rest.coinapi.io/v1/ohlcv/{symbol}/history"
STREAM_QUEUE: Queue = Queue()

# ──────────────────────────── POMOCNÉ FUNKCIE ─────────────────────────────
def load_config(path: Path) -> Dict[str, Any]:
    """Load configuration with environment variable substitution (compatible with simulate_trading_new.py)."""
    import yaml
    import os
    import re
    
    log.info(f"Loading config from: {path}")
    try:
        with open(path, 'r') as f:
            if path.suffix.lower() == '.json':
                content = f.read()
                # Replace environment variables
                def replace_env_var(match):
                    var_name = match.group(1)
                    return os.environ.get(var_name, match.group(0))
                content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
                cfg = json.loads(content)
            elif path.suffix.lower() in ['.yaml', '.yml']:
                cfg = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported config format: {path.suffix}")
    except Exception as e:
        log.error(f"Error loading/processing config {path}: {e}")
        raise

    # DEBUG: Check what API key is actually being used
    api_key = cfg.get("coinapi", {}).get("apiKey", "NOT_FOUND")
    if api_key and not api_key.startswith("${"):
        masked_key = f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else "INVALID_KEY"
        log.info(f"🔍 DEBUG: Using CoinAPI key: {masked_key}")
    elif api_key and api_key.startswith("${"):
        log.error(f"❌ ENV VARIABLE NOT SUBSTITUTED: {api_key}")
        log.error("❌ This will cause authentication failures!")
    
    log.info(f"Config loaded for symbol: {cfg.get('symbol', 'N/A')}")
    return cfg

# ―――――――――――――――――――――――― WEBSOCKET STREAM ―――――――――――――――――――――――――
async def coinapi_stream(symbol: str, tf: str, api_key: str) -> None:
    while True:
        try:
            async with websockets.connect(
                COINAPI_WS_URL,
                ping_interval=30,
                ping_timeout=10,
            ) as ws:
                # (re)subscribe
                await ws.send(json.dumps({
                    "type": "hello",
                    "apikey": api_key,
                    "heartbeat": True,          # odporúčam True
                    "subscribe_data_type": ["ohlcv", "trade", "book"],
                    "subscribe_filter_symbol_id": [symbol],
                    "subscribe_data_format": "JSON",
                    "interval": tf
                }))
                log.info("WS subscribed")
                async for m in ws:
                    try:
                        data = json.loads(m)
                        kind = data.get("type")
                        if kind == "ohlcv":
                            STREAM_QUEUE.put(("ohlcv", data))
                        elif kind == "trade":
                            STREAM_QUEUE.put(("trade", data))
                        elif kind == "book":
                            STREAM_QUEUE.put(("orderbook", data))
                    except Exception as e:      # noqa: BLE001
                        log.error(f"Stream parse error: {e}")
        except ConnectionClosedError as e:
            log.warning(f"WS connection closed, reconnect in 5s: {e}")
            await asyncio.sleep(5)
            continue
        except Exception as e:
            log.error(f"WS unexpected error: {e}, reconnect in 10s")
            await asyncio.sleep(10)
            continue

def start_stream_thread(symbol: str, tf: str, api_key: str) -> None:
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(coinapi_stream(symbol, tf, api_key))

# ──────────────────────────── HELPER FUNCTIONS ──────────────────────────────────
def _update_current_5m_candle_from_trade(data_dict, trade_ts, price, qty):
    """
    Update the current (incomplete) 5m candle with new trade data for real-time thresholds.
    This ensures we have up-to-date 5m data between completed candles.
    """
    try:
        if "5m" not in data_dict or data_dict["5m"].empty:
            return

        # Round trade timestamp to 5m boundary
        trade_time = pd.to_datetime(trade_ts, utc=True)
        minute = trade_time.minute
        rounded_minute = (minute // 5) * 5
        current_5m_start = trade_time.replace(minute=rounded_minute, second=0, microsecond=0)

        # Get the last 5m candle
        last_5m_candle = data_dict["5m"].iloc[-1]
        last_5m_time = data_dict["5m"].index[-1]

        # Check if this trade belongs to the current 5m period
        if current_5m_start == last_5m_time:
            # Update existing candle with new trade data
            data_dict["5m"].iloc[-1, data_dict["5m"].columns.get_loc('high')] = max(last_5m_candle['high'], price)
            data_dict["5m"].iloc[-1, data_dict["5m"].columns.get_loc('low')] = min(last_5m_candle['low'], price)
            data_dict["5m"].iloc[-1, data_dict["5m"].columns.get_loc('close')] = price
            data_dict["5m"].iloc[-1, data_dict["5m"].columns.get_loc('volume')] += qty

            log.debug(f"🔄 Updated current 5m candle {current_5m_start}: price=${price:.6f}, vol+={qty:.2f}")
        elif current_5m_start > last_5m_time:
            # Create new 5m candle for current period
            new_candle = pd.DataFrame({
                'open': [price],
                'high': [price],
                'low': [price],
                'close': [price],
                'volume': [qty],
                'buy_volume': [qty if qty > 0 else 0],  # Simplified - would need taker_side info
                'sell_volume': [qty if qty < 0 else 0],
                'trade_count': [1],
                'vwap': [price]
            }, index=[current_5m_start])

            # Add missing columns with default values
            for col in data_dict["5m"].columns:
                if col not in new_candle.columns:
                    new_candle[col] = 0.0

            # Reorder columns to match existing DataFrame
            new_candle = new_candle.reindex(columns=data_dict["5m"].columns, fill_value=0.0)

            # Append new candle
            data_dict["5m"] = pd.concat([data_dict["5m"], new_candle])

            log.info(f"🆕 Created new current 5m candle {current_5m_start}: price=${price:.6f}, vol={qty:.2f}")

    except Exception as e:
        log.error(f"❌ Error updating current 5m candle: {e}")


# ──────────────────────────── MAIN ENTRY ──────────────────────────────────
def trade_single_symbol(symbol: str, cfg: Dict[str, Any], binance: Any, model: Any, vecnorm: Any,
                       use_1s_decisions: bool, test_trade_mode: bool, timeframe: str) -> None:
    """Trade a single symbol with its own data streams and decision logic."""
    log.info(f"🎯 Starting trading for symbol: {symbol}")

    # Symbol-specific logging prefix
    symbol_prefix = f"[{symbol}]"

    # Create symbol-specific configuration
    symbol_cfg = cfg.copy()
    symbol_cfg["symbol"] = symbol
    symbol_cfg["dataProvider"]["symbol"] = symbol

    # CoinAPI symbol mapping (compatible with strategyConfig_scalp_1s.json)
    if symbol.startswith("BINANCEFTS_PERP_"):
        coinapi_symbol = symbol
    else:
        # For symbols like "XRPUSDC", convert to CoinAPI format
        quotes = ["USDC", "USDT", "USD", "BUSD", "BTC", "ETH", "EUR"]
        for q in quotes:
            if symbol.endswith(q):
                base  = symbol.replace("/", "")[:-len(q)]
                quote = q
                break
        else:                       # fallback: prvé 3 + zvyšok
            base, quote = symbol[:3], symbol[3:]
        coinapi_symbol = f"BINANCEFTS_PERP_{base}_{quote}"

    log.info(f"{symbol_prefix} Symbol mapping: '{symbol}' → CoinAPI='{coinapi_symbol}'")

    # Initialize symbol-specific variables
    previous_features = None
    feature_smoothing_alpha = 0.3  # Feature smoothing factor (like simulate_trading_new.py)

    # ●●●●●●●● SIGNAL SMOOTHING & STABILITY (exactly like simulate_trading_new.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    signal_history = []          # Track recent signals for stability analysis
    last_entry_signal = 0.0      # Track previous entry signal
    signal_smoothing_factor = 0.7  # Heavy smoothing for entry signals (like simulate_trading_new.py)
    min_signal_stability_count = 3  # Require 3 consecutive similar signals before triggering

    # Create deterministic seed for this symbol
    current_date = datetime.now().strftime("%Y-%m-%d")
    date_string = f"live_trading_{current_date}_{symbol}"
    deterministic_hash = int(hashlib.md5(date_string.encode()).hexdigest()[:8], 16)
    base_live_seed = deterministic_hash % (2**31)

    # Create dummy environment for this symbol
    feature_cols = symbol_cfg["envSettings"]["feature_columns"]
    lookback = symbol_cfg["envSettings"].get("state_lookback", 30)
    expected_obs_size = len(feature_cols) * lookback + 11

    dummy_df = pd.DataFrame({c: [0.0] for c in feature_cols},
                             index=[pd.Timestamp.now(tz="UTC")])
    dummy_env = DummyVecEnv([lambda: ScalpingEnv(dummy_df, symbol_cfg)])

    # Create symbol-specific TradeExecutor
    te = TradeExecutor(symbol_cfg, test_mode=symbol_cfg.get("testMode", False) or test_trade_mode)

    # Create symbol-specific data provider and queue
    symbol_queue = Queue()
    fallback_provider = FallbackDataProvider(symbol_cfg, symbol_queue)

    # Start data streams for this symbol
    timeframes_needed = [timeframe]
    if use_1s_decisions and "1s" not in timeframes_needed:
        timeframes_needed.append("1s")

    # Add timeframes needed for indicators
    indicator_settings = symbol_cfg.get("indicatorSettings", {})
    for indicator_name, indicator_config in indicator_settings.items():
        if isinstance(indicator_config, dict) and indicator_config.get("enabled", True):
            indicator_tf = indicator_config.get("tf", timeframe)
            if indicator_tf not in timeframes_needed:
                timeframes_needed.append(indicator_tf)

    timeframes_needed = list(set(timeframes_needed))
    fallback_provider.start_streams(timeframes_needed)

    # Load historical data for this symbol with WARM-UP period for indicators
    dp = DataProvider(symbol_cfg)
    end_time = datetime.now(timezone.utc)
    # WARM-UP: Load 5 extra days for 14-period indicators (ATR_14, RSI_14, ADX_14)
    start_time = end_time - timedelta(days=8)  # Increased from 3 to 8 days

    try:
        historical_5m = dp.load_historical_data(start_time, end_time, lookback_hours=192)  # 8 days * 24 hours
        log.info(f"{symbol_prefix} Loaded {len(historical_5m)} historical {timeframe} bars (includes warm-up period)")
    except Exception as e:
        log.warning(f"{symbol_prefix} Failed to load historical data: {e}")
        historical_5m = pd.DataFrame()

    # Initialize data structures for this symbol
    data_dict: Dict[str, pd.DataFrame] = {
        "trades": pd.DataFrame(columns=["price", "size", "side"]),
        "orderbooks": pd.DataFrame(columns=[f"{s}{l}" for l in range(1, 6) for s in ("bid_px", "bid_qty", "ask_px", "ask_qty")])
    }

    if use_1s_decisions:
        data_dict["1s"] = pd.DataFrame()
        data_dict["5m"] = historical_5m
    else:
        data_dict[timeframe] = historical_5m

    # Add additional timeframes
    for tf in timeframes_needed:
        if tf not in data_dict:
            data_dict[tf] = pd.DataFrame()

    # Symbol-specific trading variables
    lookback = symbol_cfg["envSettings"].get("state_lookback", 30)
    last_bar_ts = historical_5m.index[-1] if not historical_5m.empty else pd.Timestamp.utcnow()
    last_decision_ts = pd.Timestamp.utcnow()
    test_trade_executed = False

    # Evaluation tracking
    last_5m_data_ts = historical_5m.index[-1] if not historical_5m.empty else None
    last_1s_data_ts = None
    last_indicators_hash = None

    if use_1s_decisions:
        min_evaluation_interval = 1.0
        max_evaluation_interval = 10.0
    else:
        min_evaluation_interval = 5.0
        max_evaluation_interval = 60.0

    last_evaluation_time = pd.Timestamp.utcnow()

    log.info(f"{symbol_prefix} Trading setup complete, entering main loop...")

    # Main trading loop for this symbol
    iteration_count = 0
    while True:
        try:
            # Log data provider status every 30 iterations (roughly every 30 seconds)
            iteration_count += 1
            if iteration_count % 30 == 0:
                provider_status = fallback_provider.get_status()
                log.info(f"🔍 DATA PROVIDER STATUS: {provider_status}")

            # Get message from symbol-specific queue
            queue_data = symbol_queue.get(timeout=30)
            if len(queue_data) >= 3:
                typ, msg, source = queue_data[0], queue_data[1], queue_data[2]
                timeframe_received = queue_data[3] if len(queue_data) > 3 else timeframe
            else:
                typ, msg = queue_data[0], queue_data[1]
                source = "unknown"
                timeframe_received = timeframe

        except Empty:
            continue

        # Process data updates (similar to original logic but symbol-specific)
        new_5m_data = False
        new_1s_data = False

        if typ == "ohlcv":
            # Parse OHLCV data
            timestamp_field = "time_period_end"
            if timestamp_field not in msg and "time_period_start" in msg:
                if isinstance(msg.get("time_period_start"), (int, float)):
                    ts = pd.to_datetime(msg["time_period_end"], unit='ms', utc=True)
                else:
                    ts = pd.to_datetime(msg["time_period_end"], utc=True)
            else:
                ts = pd.to_datetime(msg[timestamp_field], utc=True)

            # FIX: Skip snapshot data and incomplete bars for 5m timeframe
            if timeframe_received == "5m":
                is_snapshot = bool(msg.get("is_snapshot", False))
                current_time = pd.Timestamp.utcnow()

                # Skip historical snapshots
                if is_snapshot:
                    log.debug(f"📊 Skipping 5m snapshot data: {ts}")
                    continue

                # Skip incomplete bars (bars that end in the future or very recently)
                if ts > current_time - pd.Timedelta(minutes=5):
                    log.debug(f"📊 Skipping incomplete 5m bar: {ts} (too recent)")
                    continue

                log.info(f"📊 Processing COMPLETED 5m bar: {ts}")

            try:
                row = {
                    "open": float(msg["price_open"]),
                    "high": float(msg["price_high"]),
                    "low": float(msg["price_low"]),
                    "close": float(msg["price_close"]),
                    "volume": float(msg["volume_traded"])
                }
                df = pd.DataFrame([row], index=[ts])
            except (KeyError, ValueError, TypeError) as e:
                log.warning(f"{symbol_prefix} Failed to parse OHLCV data: {e}")
                continue

            # Store data in appropriate timeframe
            detected_tf = timeframe_received

            if timeframe_received == "5m":
                detected_tf = "5m"
            elif timeframe_received == "1s":
                detected_tf = "1s"

            if detected_tf == "1s":
                data_dict["1s"] = pd.concat([data_dict["1s"], df])
                if len(data_dict["1s"]) > 1000:
                    data_dict["1s"] = data_dict["1s"].tail(1000)

                if last_1s_data_ts is None or ts > last_1s_data_ts:
                    new_1s_data = True
                    last_1s_data_ts = ts

            elif detected_tf == "5m":
                if "5m" not in data_dict:
                    data_dict["5m"] = pd.DataFrame()

                data_dict["5m"] = pd.concat([data_dict["5m"], df])
                last_bar_ts = ts

                if last_5m_data_ts is None or ts > last_5m_data_ts:
                    new_5m_data = True
                    last_5m_data_ts = ts
                    log.info(f"📊 NEW COMPLETED 5m BAR: {ts} - will trigger model recalculation")

        elif typ == "trade":
            # Handle trade data (similar to original)
            timestamp_field = msg.get("time_exchange") or msg.get("time_trade") or msg.get("T")
            if isinstance(timestamp_field, (int, float)):
                ts = pd.to_datetime(timestamp_field, unit='ms', utc=True)
            else:
                ts = pd.to_datetime(timestamp_field, utc=True)

            try:
                price = float(msg.get("price", np.nan))
                qty = float(msg.get("size") or msg.get("quantity") or msg.get("q") or np.nan)
                taker = msg.get("taker_side") or msg.get("side", "").upper()

                if "m" in msg:
                    is_buyer_maker = not msg["m"]
                elif taker == "BUY":
                    is_buyer_maker = False
                elif taker == "SELL":
                    is_buyer_maker = True
                else:
                    is_buyer_maker = np.nan
            except (ValueError, TypeError) as e:
                log.warning(f"{symbol_prefix} Failed to parse trade data: {e}")
                continue

            df = pd.DataFrame([{
                "price": price,
                "size": qty,
                "quantity": qty,
                "is_buyer_maker": is_buyer_maker
            }], index=[ts])

            if data_dict["trades"].empty:
                data_dict["trades"] = df.tail(10_000)
            else:
                data_dict["trades"] = pd.concat([data_dict["trades"], df]).tail(10_000)

            # 🚀 CRITICAL: Update current 5m candle with new trade data for real-time thresholds
            _update_current_5m_candle_from_trade(data_dict, ts, price, qty)

        # Basic data validation
        if len(data_dict[timeframe]) < lookback:
            continue

        # --- EVALUATION GUARD --------------------------------------------------
        now = pd.Timestamp.now(tz='UTC')
        latest_bar_ts = data_dict["5m"].index[-1] if not data_dict["5m"].empty else pd.Timestamp(1970,1,1, tz='UTC')

        # 1) musíme mať aspoň lookback riadkov
        if len(data_dict["5m"]) < lookback:
            continue

        # 2) musíme mať aspoň JEDNU novú uzavretú sviečku
        if latest_bar_ts < now - pd.Timedelta(minutes=5):
            # stále pracujeme so snapshotom - preskočíme evaluáciu
            log.debug(f"🔍 EVALUATION GUARD: Skipping evaluation - latest bar {latest_bar_ts} is older than {now - pd.Timedelta(minutes=5)}")
            continue

        # 3) dodrž interval medzi výpočtami
        time_since_last_eval = (now - last_evaluation_time).total_seconds()
        if time_since_last_eval < min_evaluation_interval:
            continue
        # -----------------------------------------------------------------------

        last_evaluation_time = now
        current_ts = data_dict[timeframe].index[-1] if len(data_dict[timeframe]) > 0 else now
        last_decision_ts = current_ts

        # Only log threshold information for console (reduced logging)
        # Calculate features and make trading decision
        try:
            # Debug data_dict to see if it changes
            data_dict_info = {}
            for tf, df in data_dict.items():
                if df is not None and len(df) > 0:
                    last_timestamp = df.index[-1] if hasattr(df, 'index') else 'N/A'
                    data_hash = hash(df.values.tobytes()) if hasattr(df, 'values') else 'N/A'
                    data_dict_info[tf] = f"len={len(df)}, last_ts={last_timestamp}, hash={data_hash}"
            log.info(f"🔍 DATA_DICT: {data_dict_info}")

            # Calculate indicators
            base_df, calculated_features = calculate_and_merge_indicators(data_dict, symbol_cfg)

            # Debug base_df to see if it changes
            if base_df is not None and len(base_df) > 0:
                base_df_hash = hash(base_df.values.tobytes())
                last_timestamp = base_df.index[-1] if hasattr(base_df, 'index') else 'N/A'
                log.info(f"🔍 BASE_DF: len={len(base_df)}, last_ts={last_timestamp}, hash={base_df_hash}")

            log.debug(f"🔍 DEBUG: base_df type={type(base_df)}, shape={getattr(base_df, 'shape', 'N/A')}")

            if base_df is None or len(base_df) == 0:
                continue

            # ●●●●●●●● FEATURE SMOOTHING (exactly like simulate_trading_new.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
            # Apply smoothing to the most recent features to prevent sudden changes
            if previous_features is not None and len(base_df) > 0:
                # Get current features for the latest row
                current_row = base_df.iloc[-1].copy()

                # Apply smoothing to non-OHLC features (keep price data immediate)
                ohlc_fields = ["open", "high", "low", "close", "volume"]
                smoothed_features = []

                for col in base_df.columns:
                    if col in current_row.index and col in previous_features.index:
                        if col not in ohlc_fields:  # Smooth indicators, not price data
                            old_val = previous_features[col]
                            new_val = current_row[col]

                            # Only smooth if both values are valid
                            if pd.notna(old_val) and pd.notna(new_val):
                                # Exponential smoothing: new = old * (1-α) + new * α
                                smoothed_val = old_val * (1 - feature_smoothing_alpha) + new_val * feature_smoothing_alpha
                                current_row[col] = smoothed_val
                                smoothed_features.append(col)

                # Update the DataFrame with smoothed values
                base_df.iloc[-1] = current_row

                if smoothed_features:
                    log.info(f"📊 Applied feature smoothing to {len(smoothed_features)} indicators")
                    log.debug(f"📊 Smoothed features: {smoothed_features[:5]}...")  # Log first 5

            # Store current features for next iteration
            if len(base_df) > 0:
                previous_features = base_df.iloc[-1].copy()

            # Get current price and position
            log.debug(f"🔍 DEBUG: About to access base_df['close']. base_df type={type(base_df)}")
            try:
                price_now = base_df["close"].iloc[-1]
                log.debug(f"🔍 DEBUG: Successfully extracted price_now: {price_now}")
            except Exception as e:
                log.error(f"🔍 DEBUG: Error accessing base_df['close']: {e}")
                log.error(f"🔍 DEBUG: base_df details: type={type(base_df)}, value={base_df}")
                continue
            pos, position_size, entry_price = te.get_current_position()

            # Create state vector for model
            feature_cols = symbol_cfg["envSettings"]["feature_columns"]
            log.debug(f"🔍 DEBUG: feature_cols type={type(feature_cols)}, length={len(feature_cols)}")
            log.debug(f"🔍 DEBUG: base_df.columns type={type(base_df.columns)}, length={len(base_df.columns)}")

            if len(base_df.columns) != len(feature_cols):
                log.debug(f"🔍 DEBUG: Filtering DataFrame columns from {len(base_df.columns)} to {len(feature_cols)}")
                try:
                    base_df = base_df[feature_cols]
                    log.debug(f"🔍 DEBUG: Successfully filtered DataFrame to {len(base_df.columns)} columns")
                except Exception as e:
                    log.error(f"🔍 DEBUG: Error filtering DataFrame columns: {e}")
                    log.error(f"🔍 DEBUG: feature_cols: {feature_cols}")
                    log.error(f"🔍 DEBUG: base_df.columns: {list(base_df.columns)}")
                    continue

            if len(base_df) < lookback:
                continue

            # Get recent data for state
            recent_data = base_df.tail(lookback)
            log.debug(f"🔍 DEBUG: recent_data shape={recent_data.shape}, dtypes={recent_data.dtypes.to_dict()}")

            # Create state vector - ensure we only use numeric columns and handle NaN/inf values
            try:
                # Select only numeric columns to avoid mixed types
                numeric_cols = recent_data.select_dtypes(include=[np.number]).columns
                log.debug(f"🔍 DEBUG: Using {len(numeric_cols)} numeric columns out of {len(recent_data.columns)} total")

                recent_numeric = recent_data[numeric_cols]
                log.debug(f"🔍 DEBUG: recent_numeric shape={recent_numeric.shape}")

                # Replace any remaining NaN/inf values with 0
                recent_numeric = recent_numeric.replace([np.inf, -np.inf], np.nan).fillna(0)
                log.debug(f"🔍 DEBUG: After NaN/inf cleanup: shape={recent_numeric.shape}")

                # Ensure all values are finite and of consistent type
                recent_numeric = recent_numeric.astype(np.float32)
                log.debug(f"🔍 DEBUG: After type conversion: dtype={recent_numeric.dtypes.unique()}")

                state_features = recent_numeric.values.flatten()
                log.debug(f"🔍 DEBUG: state_features shape={state_features.shape}, dtype={state_features.dtype}")

                # Final check for any problematic values
                if not np.all(np.isfinite(state_features)):
                    log.warning(f"🔍 DEBUG: Found non-finite values in state_features, replacing with 0")
                    state_features = np.nan_to_num(state_features, nan=0.0, posinf=0.0, neginf=0.0)

            except Exception as e:
                log.error(f"🔍 DEBUG: Error creating state_features: {e}")
                log.error(f"🔍 DEBUG: recent_data info: shape={recent_data.shape}")
                log.error(f"🔍 DEBUG: recent_data columns: {list(recent_data.columns)}")
                log.error(f"🔍 DEBUG: recent_data dtypes: {recent_data.dtypes}")
                log.error(f"🔍 DEBUG: recent_data sample values:")
                for col in recent_data.columns[:5]:  # Show first 5 columns
                    log.error(f"    {col}: {recent_data[col].iloc[-1]}")
                continue

            # Add meta features (trade and time features)
            current_time = pd.Timestamp.now(tz='UTC')
            trade_features = np.array([
                pos,  # current position
                0.0,  # entry_price (simplified)
                0.0,  # unrealized_pnl (simplified)
                0.0,  # sl_price (simplified)
                0.0,  # tp_price (simplified)
                0.0,  # time_in_position (simplified)
                0.0   # drawdown (simplified)
            ])

            time_features = np.array([
                current_time.hour / 24.0,
                current_time.minute / 60.0,
                current_time.weekday() / 7.0,
                current_time.day / 31.0
            ])

            state_vec = np.concatenate([state_features, trade_features, time_features]).astype(np.float32)

            # Model prediction
            if test_trade_mode and not test_trade_executed:
                action = np.array([0.8, 0.0, 0.0, 0.0], dtype=np.float32)
                test_trade_executed = True
                log.debug(f"🔍 DEBUG: Test trade action type={type(action)}, value={action}")
            else:
                try:
                    # Log state vector info to see if it changes
                    state_hash = hash(state_vec.tobytes()) if hasattr(state_vec, 'tobytes') else hash(str(state_vec))
                    log.info(f"🔍 MODEL CALL: state_vec hash={state_hash}, shape={state_vec.shape}")
                    log.info(f"🔍 MODEL CALL: state_vec sample (first 5): {state_vec[:5]}")
                    log.info(f"🔍 MODEL CALL: state_vec sample (last 5): {state_vec[-5:]}")

                    # Store previous state for comparison (use global variable)
                    global prev_state_vec
                    if 'prev_state_vec' in globals():
                        state_diff = np.abs(state_vec - prev_state_vec).max()
                        log.info(f"🔍 STATE DIFF: max_abs_diff={state_diff:.8f}")
                        if state_diff < 1e-6:
                            log.warning(f"⚠️ STATE VECTOR BARELY CHANGED: diff={state_diff:.10f}")
                    prev_state_vec = state_vec.copy()

                    # Reset model internal state to ensure fresh predictions
                    try:
                        if hasattr(model, 'actor') and hasattr(model.actor, 'reset_noise'):
                            model.actor.reset_noise()
                            log.debug("🔄 Model actor noise reset")
                        if hasattr(model.policy, 'reset_noise'):
                            model.policy.reset_noise()
                            log.debug("🔄 Model policy noise reset")
                    except Exception as reset_e:
                        log.debug(f"🔄 Model reset failed (non-critical): {reset_e}")

                    # DETERMINISTIC SEEDING for reproducible predictions (like simulate_trading_new.py)
                    time_component = int(current_time.timestamp()) % 10000  # Deterministic time component
                    step_component = int(current_time.timestamp() * 1000) % 10000  # Use timestamp instead of step number
                    state_sum = float(state_vec.sum())
                    state_component = int(abs(state_sum * 1000)) % 10000  # Deterministic state component
                    position_component = int(pos) % 100  # Position component

                    prediction_seed = (base_live_seed + time_component + step_component + state_component + position_component) % (2**31)

                    # Set seed for this prediction
                    np.random.seed(prediction_seed)
                    torch.manual_seed(prediction_seed)
                    if torch.cuda.is_available():
                        torch.cuda.manual_seed(prediction_seed)

                    log.info(f"🎲 PREDICTION SEED @ {current_time}: {prediction_seed}")
                    log.info(f"   Components: base={base_live_seed}, time={time_component}, timestamp={step_component}, state={state_component}, pos={position_component}")

                    # Prepare state exactly like simulate_trading_new.py
                    state_input = np.expand_dims(state_vec, axis=0)  # Convert to 2D array (1, 1451)

                    if vecnorm:
                        obs_normalized = vecnorm.normalize_obs(state_input)  # Keep as 2D array
                        log.info(f"🔍 DEBUG: About to call model.predict with obs_normalized shape={obs_normalized.shape}")
                        # Use 2D array like simulate_trading_new.py, not obs_normalized[0]
                        action, _ = model.predict(obs_normalized, deterministic=False)
                    else:
                        log.info(f"🔍 DEBUG: About to call model.predict with state_input shape={state_input.shape}")
                        # Use 2D array like simulate_trading_new.py
                        action, _ = model.predict(state_input, deterministic=False)

                    log.info(f"🔍 MODEL RESULT: Raw action from model={action}")
                except Exception as e:
                    log.error(f"🔍 DEBUG: Error in model.predict: {e}")
                    log.error(f"🔍 DEBUG: state_vec type={type(state_vec)}, shape={getattr(state_vec, 'shape', 'N/A')}")
                    continue

                # Ensure action is numpy array and has correct size (like simulate_trading_new.py)
                if not isinstance(action, np.ndarray):
                    log.debug(f"🔍 DEBUG: Converting action from {type(action)} to numpy array")
                    action = np.array(action, dtype=np.float32)
                    log.debug(f"🔍 DEBUG: After conversion action type={type(action)}, value={action}")

                # Handle 2D array from model.predict (flatten to 1D like simulate_trading_new.py)
                log.debug(f"🔍 DEBUG: Action before flatten check: type={type(action)}, ndim={getattr(action, 'ndim', 'N/A')}, shape={getattr(action, 'shape', 'N/A')}")
                if action.ndim > 1:
                    log.debug(f"🔍 DEBUG: Flattening 2D action array: {action.shape} -> {action.flatten().shape}")
                    action = action.flatten()
                    log.debug(f"🔍 DEBUG: Action after flatten: type={type(action)}, ndim={getattr(action, 'ndim', 'N/A')}, shape={getattr(action, 'shape', 'N/A')}")
                else:
                    log.debug(f"🔍 DEBUG: Action is already 1D, no flattening needed")

                # Check action size before accessing indices
                if action.size < 4:
                    log.error(f"🔍 DEBUG: Action has insufficient size: {action.size}, expected >= 4. Skipping.")
                    continue

            # Get ATR for risk management
            atr_now = base_df["ATR_14"].iloc[-1] if "ATR_14" in base_df.columns else 0.01

            # Extract raw signals from action
            log.debug(f"🔍 DEBUG: About to access action[0] and action[1]. Action type={type(action)}, shape={getattr(action, 'shape', 'N/A')}, size={getattr(action, 'size', 'N/A')}")
            try:
                raw_entry_sig = float(action[0])  # Use entry signal like simulate_trading_new.py
                a_tp = float(action[2]) if action.size > 2 else 0.0
                exit_sig = float(action[3]) if action.size > 3 else 0.0
                log.debug(f"🔍 DEBUG: Successfully extracted raw signals: entry={raw_entry_sig}, tp={a_tp}, exit={exit_sig}")
            except Exception as e:
                log.error(f"🔍 DEBUG: Error accessing action indices: {e}")
                log.error(f"🔍 DEBUG: Action details: type={type(action)}, value={action}")
                continue

            # ●●●●●●●● SIGNAL SMOOTHING & STABILITY (exactly like simulate_trading_new.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
            # Apply exponential smoothing to entry signal to prevent rapid changes
            if last_entry_signal != 0.0:
                # Smooth the signal: new = old * (1-α) + raw * α
                smoothed_entry_sig = last_entry_signal * (1 - signal_smoothing_factor) + raw_entry_sig * signal_smoothing_factor

                # Log smoothing effect for debugging
                smoothing_effect = abs(smoothed_entry_sig - raw_entry_sig)
                log.info(f"🎛️ SIGNAL SMOOTHING:")
                log.info(f"   Raw signal: {raw_entry_sig:.6f}")
                log.info(f"   Last signal: {last_entry_signal:.6f}")
                log.info(f"   Smoothed: {smoothed_entry_sig:.6f}")
                log.info(f"   Smoothing effect: {smoothing_effect:.6f}")
            else:
                smoothed_entry_sig = raw_entry_sig  # First signal, no smoothing

            # Update signal history for stability analysis
            signal_history.append({
                'timestamp': pd.Timestamp.utcnow(),
                'raw_signal': raw_entry_sig,
                'smoothed_signal': smoothed_entry_sig
            })

            # Keep only recent history (last 10 signals)
            if len(signal_history) > 10:
                signal_history.pop(0)

            # ●●●●●●●● SIGNAL STABILITY CHECK (prevent rapid threshold triggers) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
            # Check if signal is stable enough for threshold evaluation
            signal_stable = True
            if len(signal_history) >= min_signal_stability_count:
                # Check last N signals for consistency
                recent_signals = [s['smoothed_signal'] for s in signal_history[-min_signal_stability_count:]]
                signal_variance = np.var(recent_signals)
                signal_mean = np.mean(recent_signals)

                # Require low variance for stability
                stability_threshold = 0.1  # Signals must be within 0.1 variance
                signal_stable = signal_variance < stability_threshold

                log.info(f"🔒 STABILITY CHECK:")
                log.info(f"   Recent signals: {[f'{s:.3f}' for s in recent_signals]}")
                log.info(f"   Mean: {signal_mean:.6f}, Variance: {signal_variance:.6f}")
                log.info(f"   Stable: {signal_stable} (threshold: {stability_threshold})")

            # Use smoothed signal for threshold evaluation
            entry_sig = smoothed_entry_sig
            last_entry_signal = smoothed_entry_sig

            # Convert entry signal to long/short signals for compatibility
            long_signal = entry_sig if entry_sig > 0 else 0.0
            short_signal = entry_sig if entry_sig < 0 else 0.0

            # Get thresholds from config
            long_threshold = symbol_cfg.get("longThreshold", 0.5)
            short_threshold = symbol_cfg.get("shortThreshold", -0.5)

            # Only log when signals are close to or exceed thresholds
            if abs(long_signal - long_threshold) < 0.1 or long_signal > long_threshold:
                log.info(f"{symbol_prefix} LONG: {long_signal:.3f} vs {long_threshold:.3f} | Price: ${price_now:.4f}")

            if abs(short_signal - short_threshold) < 0.1 or short_signal < short_threshold:
                log.info(f"{symbol_prefix} SHORT: {short_signal:.3f} vs {short_threshold:.3f} | Price: ${price_now:.4f}")

            # Execute trade only if signal is stable (like simulate_trading_new.py)
            if signal_stable:
                # Reconstruct action array with smoothed entry signal for TradeExecutor
                smoothed_action = np.array([entry_sig, 0.0, a_tp, exit_sig], dtype=np.float32)

                log.debug(f"🔍 DEBUG: About to call te.execute_with_binance with smoothed action")
                log.debug(f"🔍 DEBUG: Original action: {action}")
                log.debug(f"🔍 DEBUG: Smoothed action: {smoothed_action}")
                log.debug(f"🔍 DEBUG: price_now type={type(price_now)}, value={price_now}")
                log.debug(f"🔍 DEBUG: atr_now type={type(atr_now)}, value={atr_now}")

                try:
                    te.execute_with_binance(binance, smoothed_action, price_now, atr_now)
                    log.debug(f"🔍 DEBUG: te.execute_with_binance completed successfully")
                except Exception as e:
                    log.error(f"🔍 DEBUG: Error in te.execute_with_binance: {e}")
                    log.error(f"🔍 DEBUG: action details: type={type(smoothed_action)}, value={smoothed_action}")
                    log.error(f"🔍 DEBUG: current_price: {price_now}")
                    log.error(f"🔍 DEBUG: current_atr: {atr_now}")
                    import traceback
                    log.error(f"🔍 DEBUG: Full traceback: {traceback.format_exc()}")
                    continue
            else:
                log.info(f"🔒 SIGNAL NOT STABLE - Skipping trade execution")
                log.info(f"   Entry signal: {entry_sig:.6f}")
                log.info(f"   Signal variance too high for safe trading")

        except Exception as e:
            log.error(f"{symbol_prefix} Error in trading logic: {e}")
            import traceback
            log.error(f"{symbol_prefix} Full traceback: {traceback.format_exc()}")
            continue


def main() -> None:
    parser = argparse.ArgumentParser("Live trading with SAC + TradeExecutor")
    parser.add_argument("--cfg", type=Path, default=DEFAULT_CONFIG_PATH)
    parser.add_argument("--log-level", default="INFO",
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--use-1s-decisions", "--use-1s-decision", action="store_true",
                        help="Use 1s decision frequency with 5m features (forward-filled)")
    parser.add_argument("--test-trade", action="store_true",
                        help="Force one test LONG trade to verify TradeExecutor functionality")
    parser.add_argument("--symbols", type=str, nargs='+',
                        help="List of symbols to trade (e.g., --symbols XRPUSDC LINKUSDC ADAUSDC)")
    args = parser.parse_args()

    log.setLevel(args.log_level.upper())
    cfg = load_config(args.cfg)

    # ●●●●●●●● DETERMINISTIC SEED MANAGEMENT FOR REPRODUCIBLE LIVE TRADING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    # For live_trading: Use current date as base for deterministic seeding
    current_date = datetime.now().strftime("%Y-%m-%d")
    date_string = f"live_trading_{current_date}"
    deterministic_hash = int(hashlib.md5(date_string.encode()).hexdigest()[:8], 16)
    base_live_seed = deterministic_hash % (2**31)  # Use deterministic hash
    log.info(f"🔒 LIVE TRADING SEED: {base_live_seed} (deterministic hash of: {date_string})")
    log.info(f"🎯 DETERMINISTIC MODE: Model will behave consistently for same market conditions")
    log.info(f"📊 This ensures reproducible trading decisions and easier debugging")

    # 🎯 LIVE TRADING: Use DETERMINISTIC seeds for consistent model behavior
    # Model is already trained - we want consistent behavior for same market conditions
    live_seed = base_live_seed  # Use deterministic seed for live trading
    random.seed(live_seed)
    np.random.seed(live_seed)
    torch.manual_seed(live_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(live_seed)
        torch.cuda.manual_seed_all(live_seed)

    # Ensure deterministic behavior for live trading
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = True
    log.info(f"🎲 LIVE TRADING SEED: {live_seed} (non-deterministic for real-time trading)")
    log.info(f"🔒 BASE SEED DETERMINISTIC: {base_live_seed} (will be same for same date)")
    log.info(f"⚡ NON-DETERMINISTIC MODE: Model will respond naturally to market changes")

    # Compatible with strategyConfig_scalp_1s.json structure
    # Support multiple symbols from command line or config
    if args.symbols:
        symbols_list = args.symbols
        log.info(f"🎯 Using symbols from command line: {symbols_list}")
    else:
        # Single symbol from config (backward compatibility)
        symbol_cfg = cfg.get("symbol") or cfg.get("dataProvider", {}).get("symbol", "XRPUSDC")
        symbols_list = [symbol_cfg]
        log.info(f"🎯 Using single symbol from config: {symbol_cfg}")

    timeframe    = cfg["primaryTimeframe"]                # napr. 1s alebo 5m
    coinapi_key  = cfg["coinapi"]["apiKey"]
    use_1s_decisions = args.use_1s_decisions or cfg.get("use_1s_decisions", False)
    test_trade_mode = args.test_trade

    log.info(f"🚀 Starting live trading for {len(symbols_list)} symbol(s): {', '.join(symbols_list)}")
    
    # Check for missing API keys
    if not coinapi_key or coinapi_key.startswith("${"):
        log.error("❌ CoinAPI key not configured!")
        log.error("Please set COINAPI_KEY environment variable or create .env file")
        log.error("See .env.example for configuration template")
        sys.exit(1)

    if use_1s_decisions:
        log.info("🚀 Using 1s decision frequency with 5m features (forward-filled)")
        decision_timeframe = "1s"
    else:
        log.info(f"Using {timeframe} decision frequency")
        decision_timeframe = timeframe

    if test_trade_mode:
        log.info("🧪 TEST TRADE MODE ENABLED")
        log.info("🧪 Will force one LONG trade to verify TradeExecutor functionality")
        log.info("🧪 Expected: Entry signal=0.8 > 0.5 threshold → LONG position with SL/TP")

    # Binance client (odovzdáme ho aj TradeExecutor‑u)
    if not BINANCE_AVAILABLE:
        log.error("❌ Binance client not available - python-binance not installed!")
        log.error("Install with: pip install python-binance")
        sys.exit(1)
    
    # Handle Binance API keys from environment variables
    binance_api_key = cfg["binance"]["apiKey"]
    binance_api_secret = cfg["binance"]["apiSecret"]
    
    # Replace environment variables if needed
    if binance_api_key.startswith("${"):
        var_name = binance_api_key.replace("${", "").replace("}", "")
        binance_api_key = os.environ.get(var_name, "")
    if binance_api_secret.startswith("${"):
        var_name = binance_api_secret.replace("${", "").replace("}", "")
        binance_api_secret = os.environ.get(var_name, "")
    
    binance = BinanceClient(binance_api_key, binance_api_secret)
    
    # Setup futures trading if enabled
    if cfg.get("binance", {}).get("futures", False):
        log.info("🚀 Setting up Binance Futures trading...")

        # Get futures settings
        leverage = cfg.get("binance", {}).get("leverage", 10)
        margin_type = cfg.get("binance", {}).get("marginType", "ISOLATED")

        # Setup futures for each symbol
        for symbol_name in symbols_list:
            symbol = symbol_name  # Use the symbol from symbols list

            # Extract futures symbol from CoinAPI format if needed
            if symbol.startswith('BINANCEFTS_PERP_'):
                parts = symbol.replace('BINANCEFTS_PERP_', '').split('_')
                symbol = ''.join(parts) if len(parts) >= 2 else symbol

            try:
                # Set leverage for futures trading
                if not cfg.get("testMode", False):  # Only set leverage in live mode
                    try:
                        binance.futures_change_leverage(symbol=symbol, leverage=leverage)
                        log.info(f"✅ Set leverage to {leverage}x for {symbol}")
                    except Exception as e:
                        log.warning(f"Failed to set leverage for {symbol}: {e}")
                        if "does not exist" in str(e).lower():
                            log.error(f"❌ Symbol {symbol} does not exist in futures market")
                        # Continue without failing

                    # Set margin type with better error handling
                    try:
                        binance.futures_change_margin_type(symbol=symbol, marginType=margin_type)
                        log.info(f"✅ Set margin type to {margin_type} for {symbol}")
                    except Exception as e:
                        log.warning(f"Failed to set margin type for {symbol}: {e}")
                        # Check for specific credit/balance issues
                        if "credit status" in str(e).lower() or "insufficient" in str(e).lower():
                            log.warning(f"⚠️ Account credit/balance issue for {symbol} - continuing with default margin mode")
                            log.warning(f"   You may need to verify account status or add more balance")
                        elif "mode" in str(e).lower():
                            log.warning(f"⚠️ Margin mode setting failed for {symbol} - using account default")
                        # Continue without failing - margin mode is often optional

                else:
                    log.info(f"🧪 TEST MODE: Would set leverage={leverage}x, margin={margin_type} for {symbol}")

            except Exception as e:
                log.warning(f"General futures setup error for {symbol}: {e}")
                log.warning(f"⚠️ Futures setup had issues for {symbol} but continuing - trading may still work")
                # Don't fail completely - many futures settings are optional
    
    cfg["binance_client"] = binance                       # uložíme do cfg

    # ------------------------- CONFIGURATION SUMMARY --------------------------
    log.info(f"🔄 Configuration loaded:")
    log.info(f"   Symbols: {', '.join(symbols_list)}")
    log.info(f"   Primary Timeframe: {timeframe}")
    log.info(f"   Use 1s decisions: {use_1s_decisions}")
    log.info(f"   Test mode: {test_trade_mode}")

    if test_trade_mode:
        log.info("🧪 TEST MODE ENABLED - no real orders will be placed")
    else:
        log.info("💰 LIVE MODE - real orders will be placed!")
    
    # Model path detection with .zip handling
    model_path_str = cfg["trainingSettings"]["modelSavePath"]
    if not model_path_str.endswith(".zip"):
        model_path_str += ".zip"
    model_path = Path(model_path_str)
    
    # Auto-detect available model if specified doesn't exist
    if not model_path.exists():
        available_models = list(Path(".").glob("sac_*_steps.zip"))
        if available_models:
            # Sort by timesteps and take the latest
            available_models.sort(key=lambda x: int(x.stem.split('_')[1]))
            model_path = available_models[-1]
            log.info(f"Auto-detected model: {model_path}")
        else:
            log.error(f"No model found at {model_path} and no auto-detectable models!")
            sys.exit(1)
    
    # VecNormalize path detection
    vecnorm_path = model_path.with_suffix('.vecnorm.pkl')
    if not vecnorm_path.exists():
        model_stem = model_path.stem.replace('_steps', '')
        vecnorm_path = Path(f"{model_stem}.vecnorm.pkl")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    log.info(f"Using model: {model_path}")
    log.info(f"Using vecnorm: {vecnorm_path}")

    # --- Calculate expected observation space size like simulate_trading_new.py ---
    feature_cols = cfg["envSettings"]["feature_columns"]
    lookback = cfg["envSettings"].get("state_lookback", 30)
    
    # Calculate expected observation space: features * lookback + meta_features
    # Meta features: 7 trade features + 4 time features = 11 total
    expected_obs_size = len(feature_cols) * lookback + 11
    
    log.info(f"🔧 Environment setup:")
    log.info(f"   Feature columns: {len(feature_cols)} features")
    log.info(f"   Lookback: {lookback}")
    log.info(f"   Expected observation space: {expected_obs_size}")
    log.info(f"   Features sample: {feature_cols[:5]}{'...' if len(feature_cols) > 5 else ''}")
    
    # Create dummy environment with correct observation space
    dummy_df = pd.DataFrame({c: [0.0] for c in feature_cols},
                             index=[pd.Timestamp.now(tz="UTC")])
    dummy_env = DummyVecEnv([lambda: ScalpingEnv(dummy_df, cfg)])

    # DEBUG: Check actual observation space
    log.info(f"🔍 DEBUG: Dummy environment observation space: {dummy_env.observation_space.shape}")
    log.info(f"🔍 DEBUG: Expected observation space: {expected_obs_size}")
    log.info(f"🔍 DEBUG: ScalpingEnv feature_cols from cfg: {len(cfg['envSettings']['feature_columns'])}")
    log.info(f"🔍 DEBUG: ScalpingEnv lookback from cfg: {cfg['envSettings'].get('state_lookback', 30)}")
    
    # Load VecNormalize if available - with proper error handling for shape mismatch
    vecnorm = None
    if vecnorm_path.exists():
        try:
            vecnorm = VecNormalize.load(vecnorm_path, venv=dummy_env)
            vecnorm.training = False
            log.info("✅ VecNormalize loaded successfully")
            
            # Verify observation space compatibility
            if hasattr(vecnorm, 'observation_space'):
                vecnorm_obs_shape = vecnorm.observation_space.shape[0] if hasattr(vecnorm.observation_space, 'shape') else None
                if vecnorm_obs_shape and vecnorm_obs_shape != expected_obs_size:
                    log.warning(f"⚠️ VecNormalize observation space mismatch:")
                    log.warning(f"   VecNormalize expects: {vecnorm_obs_shape}")
                    log.warning(f"   Current config expects: {expected_obs_size}")
                    log.warning(f"   This may cause prediction errors!")
            
        except Exception as e:
            log.warning(f"Failed to load VecNormalize: {e}")
            if "spaces must have the same shape" in str(e):
                log.warning(f"⚠️ VecNormalize shape mismatch - model was trained with different feature set")
                log.warning(f"   Model expectation vs current config mismatch")
                log.warning(f"   Consider using the exact same config as during training")
            vecnorm = None
    else:
        log.warning(f"VecNormalize not found: {vecnorm_path}")

    # Load final model with correct configuration
    log.info("📦 Loading final PopArt SAC model...")
    
    # Get training settings from config
    training_settings = cfg.get("trainingSettings", {})
    
    # Policy kwargs for PopArt SAC with corrected feature columns
    policy_kwargs = {
        "net_arch": training_settings.get("netArch", [512, 256]),
        "features_extractor_class": SimpleCNN1D,
        "features_extractor_kwargs": {
            **training_settings.get("featureExtractorKwargs", {
                "lookback": 30,
                "n_filters": 128,
                "kernel": 3,
                "out_dim": 256
            }),
            "feature_names": feature_cols,  # Use corrected feature_cols
            "meta_len": 11
        }
    }
    
    # Initialize custom_objects dictionary
    custom_objects = {
        "buffer_size": training_settings.get("bufferSize", 5000000),
        "policy_kwargs": policy_kwargs,
        "replay_buffer_class": SafeReplayBuffer,
        "learning_rate": 0.0001
    }

    try:
        model = PopArtSAC.load(model_path, device=device, custom_objects=custom_objects)
        log.info(f"✅ PopArt SAC model loaded successfully from {model_path}")
        log.info(f"   Model observation space: {model.observation_space.shape}")
        log.info(f"   Using {len(feature_cols)} features for compatibility")
    except Exception as e:
        log.error(f"❌ Failed to load PopArt model: {e}")
        log.warning("🤖 Using DummyModel fallback for testing")
        class DummyModel:                       # pylint: disable=too-few-public-methods
            def predict(self, obs, deterministic=False):
                # For testing: return signal that will trigger LONG entry (0.8 > 0.1 threshold)
                return np.array([0.8, 0.0, 0.0, 0.0], dtype=np.float32), None
            @property
            def observation_space(self):
                from gym.spaces import Box
                return Box(low=-np.inf, high=np.inf, shape=(expected_obs_size,))
        model = DummyModel()

    # ------------------------- štart FALLBACK DATA PROVIDER ----------------------------
    log.info("🔄 Starting Fallback Data Provider (CoinAPI + Binance fallback)...")
    
    # Create enhanced data queue for fallback provider
    ENHANCED_STREAM_QUEUE = Queue()
    
    # Initialize fallback data provider
    fallback_provider = FallbackDataProvider(cfg, ENHANCED_STREAM_QUEUE)
    
    # Start data streams with fallback
    timeframes_needed = [timeframe]
    if use_1s_decisions and "1s" not in timeframes_needed:
        timeframes_needed.append("1s")
    
    # CRITICAL FIX: Add timeframes needed for indicators
    # Scan indicator settings to find required timeframes
    indicator_settings = cfg.get("indicatorSettings", {})
    for indicator_name, indicator_config in indicator_settings.items():
        if isinstance(indicator_config, dict) and indicator_config.get("enabled", True):
            indicator_tf = indicator_config.get("tf", timeframe)
            if indicator_tf not in timeframes_needed:
                timeframes_needed.append(indicator_tf)
                log.info(f"🔧 Added timeframe '{indicator_tf}' for indicator '{indicator_name}'")
    
    # Remove duplicates
    timeframes_needed = list(set(timeframes_needed))
    
    fallback_provider.start_streams(timeframes_needed)
    log.info(f"✅ Data streams started for timeframes: {timeframes_needed}")

    # ------------------------- LOAD HISTORICAL DATA ---------------------------
    log.info("🔄 Loading historical data from parquet files before starting WebSocket...")
    

        
    # Wait a moment for initial 5m data to load
    log.info("🔄 Waiting for initial 5m data to load...")
    time.sleep(5)  # Give the REST API time to load initial data
    log.info("✅ Proceeding with live trading")
    
    # ------------------------- init dátový dict ---------------------------
    data_dict: Dict[str, pd.DataFrame] = {
        "trades"    : pd.DataFrame(columns=["price", "size", "side"]),
        "orderbooks": pd.DataFrame(columns=[f"{s}{l}"
                                for l in range(1, 6)
                                for s in ("bid_px", "bid_qty", "ask_px", "ask_qty")])
    }
    
    # CRITICAL FIX: Properly initialize timeframe data based on actual timeframes
    # Initialize empty DataFrames for historical data (will be loaded in each thread)
    historical_1s = pd.DataFrame()
    historical_5m = pd.DataFrame()

    if use_1s_decisions:
        # For 1s decisions, we need both 1s and 5m data
        data_dict["1s"] = historical_1s         # Pre-populated with historical 1s OHLCV
        data_dict["5m"] = historical_5m         # Pre-populated with historical 5m OHLCV
        log.info("✅ Initialized data_dict with 1s and 5m timeframes")
    else:
        # For non-1s decisions, use the primary timeframe
        data_dict[timeframe] = historical_5m    # Primary timeframe data
        log.info(f"✅ Initialized data_dict with {timeframe} timeframe")
    
    # Add any additional timeframes needed for indicators
    for tf in timeframes_needed:
        if tf not in data_dict:
            data_dict[tf] = pd.DataFrame()      # Empty DataFrame for live data
            log.info(f"✅ Added empty DataFrame for timeframe '{tf}'")

    lookback = cfg["envSettings"].get("state_lookback", 30)
    last_bar_ts = historical_5m.index[-1] if not historical_5m.empty else pd.Timestamp.utcnow()
    last_decision_ts = pd.Timestamp.utcnow()
    test_trade_executed = False  # Flag to ensure test trade only executes once

    # Feature smoothing for gradual updates when new 5m data arrives
    previous_features = None
    feature_smoothing_alpha = 0.3  # Smoothing factor for gradual feature updates

    # ●●●●●●●● SIGNAL SMOOTHING & STABILITY (exactly like simulate_trading_new.py) ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    signal_history = []          # Track recent signals for stability analysis
    last_entry_signal = 0.0      # Track previous entry signal
    signal_smoothing_factor = 0.7  # Heavy smoothing for entry signals (like simulate_trading_new.py)
    min_signal_stability_count = 3  # Require 3 consecutive similar signals before triggering

    # ●●●●●●●● INTELLIGENT EVALUATION TRACKING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    last_5m_data_ts = historical_5m.index[-1] if not historical_5m.empty else None
    last_1s_data_ts = historical_1s.index[-1] if not historical_1s.empty else None
    last_indicators_hash = None  # Hash of key indicators to detect changes
    # Adjust evaluation intervals based on decision mode
    if use_1s_decisions:
        min_evaluation_interval = 1.0   # 1 second for 1s decisions
        max_evaluation_interval = 10.0  # 10 seconds failsafe for 1s mode
    else:
        min_evaluation_interval = 5.0   # 5 seconds for 5m decisions
        max_evaluation_interval = 60.0  # 60 seconds failsafe for 5m mode
    last_evaluation_time = pd.Timestamp.utcnow()
    
    log.info(f"🎯 INTELLIGENT EVALUATION SETUP:")
    log.info(f"   Min evaluation interval: {min_evaluation_interval}s")
    log.info(f"   Max evaluation interval: {max_evaluation_interval}s")
    log.info(f"   Last 5m data timestamp: {last_5m_data_ts}")
    log.info(f"   Last 1s data timestamp: {last_1s_data_ts}")
    
    # Log data status
    if not historical_5m.empty:
        log.info(f"✅ Historical {timeframe} data loaded: {len(historical_5m)} bars from {historical_5m.index[0]} to {historical_5m.index[-1]}")
    else:
        log.warning(f"⚠️ No historical {timeframe} data - will wait for WebSocket data")
        
    if use_1s_decisions and not historical_1s.empty:
        log.info(f"✅ Historical 1s data loaded: {len(historical_1s)} bars from {historical_1s.index[0]} to {historical_1s.index[-1]}")
    elif use_1s_decisions:
        log.warning("⚠️ No historical 1s data - will wait for WebSocket data")

    # ──────────────────────── MULTI-SYMBOL TRADING ───────────────────────────────

    # Start trading threads for each symbol
    trading_threads = []

    for symbol in symbols_list:
        log.info(f"🚀 Starting trading thread for {symbol}")

        # Create thread for this symbol
        thread = threading.Thread(
            target=trade_single_symbol,
            args=(symbol, cfg, binance, model, vecnorm, use_1s_decisions, test_trade_mode, timeframe),
            name=f"Trading-{symbol}",
            daemon=True
        )

        trading_threads.append(thread)
        thread.start()

        # Small delay between starting threads
        time.sleep(1)

    log.info(f"✅ All {len(symbols_list)} trading threads started")
    log.info("📊 Console will show only threshold information for each symbol")
    log.info("📝 Full trading logs are written to individual log files")

    # Main thread waits for all trading threads
    try:
        while True:
            # Check if all threads are still alive
            alive_threads = [t for t in trading_threads if t.is_alive()]

            if len(alive_threads) < len(trading_threads):
                dead_threads = [t for t in trading_threads if not t.is_alive()]
                log.warning(f"⚠️ {len(dead_threads)} trading thread(s) died: {[t.name for t in dead_threads]}")

                # Optionally restart dead threads
                for dead_thread in dead_threads:
                    symbol = dead_thread.name.replace("Trading-", "")
                    log.info(f"🔄 Restarting trading thread for {symbol}")

                    new_thread = threading.Thread(
                        target=trade_single_symbol,
                        args=(symbol, cfg, binance, model, vecnorm, use_1s_decisions, test_trade_mode, timeframe),
                        name=f"Trading-{symbol}",
                        daemon=True
                    )

                    # Replace dead thread in list
                    for i, t in enumerate(trading_threads):
                        if t.name == dead_thread.name:
                            trading_threads[i] = new_thread
                            break

                    new_thread.start()

            # Sleep for a while before checking again
            time.sleep(30)

    except KeyboardInterrupt:
        log.info("🛑 Keyboard interrupt received - shutting down...")

        # Wait for all threads to finish
        for thread in trading_threads:
            if thread.is_alive():
                log.info(f"⏳ Waiting for {thread.name} to finish...")
                thread.join(timeout=5)

        log.info("✅ All trading threads stopped")

    except Exception as e:
        log.error(f"❌ Unexpected error in main thread: {e}")
        raise


if __name__ == "__main__":
    sys.exit(main())
