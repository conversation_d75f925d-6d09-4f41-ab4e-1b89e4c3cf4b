2025-07-18 15:12:17,127 INFO  Loading config from: strategyConfig_scalp_1s.json
2025-07-18 15:12:17,128 INFO  🔍 DEBUG: Using CoinAPI key: a49bb33b...1cef
2025-07-18 15:12:17,128 INFO  Config loaded for symbol: XRPUSDC
2025-07-18 15:12:17,128 INFO  🔒 LIVE TRADING SEED: 1293771362 (deterministic hash of: live_trading_2025-07-18)
2025-07-18 15:12:17,128 INFO  🎯 DETERMINISTIC MODE: Model will behave consistently for same market conditions
2025-07-18 15:12:17,128 INFO  📊 This ensures reproducible trading decisions and easier debugging
2025-07-18 15:12:17,129 INFO  🎲 LIVE TRADING SEED: 1293771362 (non-deterministic for real-time trading)
2025-07-18 15:12:17,129 INFO  🔒 BASE SEED DETERMINISTIC: 1293771362 (will be same for same date)
2025-07-18 15:12:17,129 INFO  ⚡ NON-DETERMINISTIC MODE: Model will respond naturally to market changes
2025-07-18 15:12:17,129 INFO  🎯 Using single symbol from config: XRPUSDC
2025-07-18 15:12:17,130 INFO  🚀 Starting live trading for 1 symbol(s): XRPUSDC
2025-07-18 15:12:17,130 INFO  🚀 Using 1s decision frequency with 5m features (forward-filled)
2025-07-18 15:12:17,130 INFO  🧪 TEST TRADE MODE ENABLED
2025-07-18 15:12:17,130 INFO  🧪 Will force one LONG trade to verify TradeExecutor functionality
2025-07-18 15:12:17,130 INFO  🧪 Expected: Entry signal=0.8 > 0.5 threshold → LONG position with SL/TP
2025-07-18 15:12:17,882 INFO  🚀 Setting up Binance Futures trading...
2025-07-18 15:12:18,628 WARNING Failed to set leverage for XRPUSDC: APIError(code=-2015): Invalid API-key, IP, or permissions for action, request ip: *************
2025-07-18 15:12:18,947 WARNING Failed to set margin type for XRPUSDC: APIError(code=-2015): Invalid API-key, IP, or permissions for action, request ip: *************
2025-07-18 15:12:18,947 INFO  🔄 Configuration loaded:
2025-07-18 15:12:18,947 INFO     Symbols: XRPUSDC
2025-07-18 15:12:18,947 INFO     Primary Timeframe: 1s
2025-07-18 15:12:18,947 INFO     Use 1s decisions: True
2025-07-18 15:12:18,947 INFO     Test mode: True
2025-07-18 15:12:18,947 INFO  🧪 TEST MODE ENABLED - no real orders will be placed
2025-07-18 15:12:18,947 INFO  Using model: sac_9996800_steps.zip
2025-07-18 15:12:18,947 INFO  Using vecnorm: sac_9996800.vecnorm.pkl
2025-07-18 15:12:18,947 INFO  🔧 Environment setup:
2025-07-18 15:12:18,947 INFO     Feature columns: 48 features
2025-07-18 15:12:18,947 INFO     Lookback: 30
2025-07-18 15:12:18,947 INFO     Expected observation space: 1451
2025-07-18 15:12:18,947 INFO     Features sample: ['open', 'high', 'low', 'close', 'volume']...
2025-07-18 15:12:18,949 INFO  🔍 DEBUG: Dummy environment observation space: (1451,)
2025-07-18 15:12:18,949 INFO  🔍 DEBUG: Expected observation space: 1451
2025-07-18 15:12:18,949 INFO  🔍 DEBUG: ScalpingEnv feature_cols from cfg: 48
2025-07-18 15:12:18,949 INFO  🔍 DEBUG: ScalpingEnv lookback from cfg: 30
2025-07-18 15:12:18,959 INFO  ✅ VecNormalize loaded successfully
2025-07-18 15:12:18,959 INFO  📦 Loading final PopArt SAC model...
2025-07-18 15:12:19,892 INFO  ✅ PopArt SAC model loaded successfully from sac_9996800_steps.zip
2025-07-18 15:12:19,892 INFO     Model observation space: (1451,)
2025-07-18 15:12:19,892 INFO     Using 48 features for compatibility
2025-07-18 15:12:19,892 INFO  🔄 Starting Fallback Data Provider (CoinAPI + Binance fallback)...
2025-07-18 15:12:19,892 INFO  🔍 DEBUG: FallbackDataProvider using CoinAPI key: a49bb33b...1cef
2025-07-18 15:12:19,892 INFO  FallbackDataProvider initialized:
2025-07-18 15:12:19,892 INFO    Original symbol: XRPUSDC
2025-07-18 15:12:19,892 INFO    Binance symbol: xrpusdc
2025-07-18 15:12:19,892 INFO    CoinAPI symbol: BINANCEFTS_PERP_XRP_USDC
2025-07-18 15:12:19,892 INFO  🔧 Added timeframe '5m' for indicator 'hmm_5m'
2025-07-18 15:12:19,892 INFO  💎 Starting CoinAPI-ONLY data streams for timeframes: ['1s', '5m']
2025-07-18 15:12:19,892 INFO  🔄 Fetching initial 5m data for indicators...
2025-07-18 15:12:19,892 INFO  🔄 Fetching initial 5m data from CoinAPI REST API...
2025-07-18 15:12:19,892 INFO  🔍 Fetching 5m data from 2025-07-17T04:12:19.000Z to 2025-07-18T13:12:19.000Z
2025-07-18 15:12:19,893 INFO  🔧 WARM-UP: Loading 33.0 hours of data for indicator calculation
